// Form Builder main entry point
import '../main.js' // Import base styles and setup
import { createApp } from 'vue'
import FormBuilderMain from './components/FormBuilderMain.vue'
import { createFormBuilderStore } from './stores/formBuilderStore'

// Global styles
import './styles/main.css'

// Create and mount the Vue app
window.initFormBuilder = function(elementId, formData, csrfToken) {
  const app = createApp({
    components: {
      FormBuilderMain
    },
    data() {
      return {
        formData: formData || {},
        csrfToken: csrfToken || ''
      }
    },
    template: `
      <form-builder-main
        :form-data="formData"
        :csrf-token="csrfToken"
      ></form-builder-main>
    `
  })

  // Create and provide the store
  const store = createFormBuilderStore()
  app.provide('formBuilderStore', store)

  // Global error handler
  app.config.errorHandler = (err, instance, info) => {
    console.error('Form Builder Error:', err, info)
  }

  // Mount the app
  app.mount(elementId)

  return app
}

// Initialize the form builder application
document.addEventListener('DOMContentLoaded', () => {
  const formBuilderElement = document.getElementById('form-builder-app')

  if (formBuilderElement && window.formBuilderData) {
    window.initFormBuilder(
      '#form-builder-app',
      window.formBuilderData.formData,
      window.formBuilderData.csrfToken
    )
  }
})
