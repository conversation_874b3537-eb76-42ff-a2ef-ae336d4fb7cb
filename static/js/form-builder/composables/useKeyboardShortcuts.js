import { onMounted, onUnmounted } from 'vue'

export function useKeyboardShortcuts(store) {
  const shortcuts = {
    // Undo/Redo
    'ctrl+z': () => store.actions.undo(),
    'cmd+z': () => store.actions.undo(),
    'ctrl+y': () => store.actions.redo(),
    'cmd+y': () => store.actions.redo(),
    'ctrl+shift+z': () => store.actions.redo(),
    'cmd+shift+z': () => store.actions.redo(),
    
    // Copy/Paste
    'ctrl+c': () => {
      if (store.state.selectedField) {
        store.actions.copyToClipboard(store.state.selectedField)
      }
    },
    'cmd+c': () => {
      if (store.state.selectedField) {
        store.actions.copyToClipboard(store.state.selectedField)
      }
    },
    'ctrl+v': () => store.actions.pasteFromClipboard(),
    'cmd+v': () => store.actions.pasteFromClipboard(),
    
    // Duplicate
    'ctrl+d': () => {
      if (store.state.selectedField) {
        store.actions.duplicateField(store.state.selectedField)
      } else if (store.state.selectedFields.length > 0) {
        store.actions.duplicateSelectedFields()
      }
    },
    'cmd+d': () => {
      if (store.state.selectedField) {
        store.actions.duplicateField(store.state.selectedField)
      } else if (store.state.selectedFields.length > 0) {
        store.actions.duplicateSelectedFields()
      }
    },
    
    // Delete
    'delete': () => {
      if (store.state.selectedField) {
        store.actions.deleteField(store.state.selectedField.id)
      } else if (store.state.selectedFields.length > 0) {
        store.actions.deleteSelectedFields()
      }
    },
    'backspace': () => {
      if (store.state.selectedField) {
        store.actions.deleteField(store.state.selectedField.id)
      } else if (store.state.selectedFields.length > 0) {
        store.actions.deleteSelectedFields()
      }
    },
    
    // Select All
    'ctrl+a': (event) => {
      event.preventDefault()
      store.actions.selectAllFields()
    },
    'cmd+a': (event) => {
      event.preventDefault()
      store.actions.selectAllFields()
    },
    
    // Escape - Clear selection
    'escape': () => {
      store.actions.clearSelection()
      store.actions.clearSelection()
    },
    
    // Save
    'ctrl+s': (event) => {
      event.preventDefault()
      // Trigger save action
      document.dispatchEvent(new CustomEvent('form-builder:save'))
    },
    'cmd+s': (event) => {
      event.preventDefault()
      // Trigger save action
      document.dispatchEvent(new CustomEvent('form-builder:save'))
    },
    
    // Preview
    'ctrl+p': (event) => {
      event.preventDefault()
      // Trigger preview action
      document.dispatchEvent(new CustomEvent('form-builder:preview'))
    },
    'cmd+p': (event) => {
      event.preventDefault()
      // Trigger preview action
      document.dispatchEvent(new CustomEvent('form-builder:preview'))
    }
  }
  
  const getKeyCombo = (event) => {
    const keys = []
    
    if (event.ctrlKey) keys.push('ctrl')
    if (event.metaKey) keys.push('cmd')
    if (event.shiftKey) keys.push('shift')
    if (event.altKey) keys.push('alt')
    
    // Add the main key
    const key = event.key.toLowerCase()
    if (key !== 'control' && key !== 'meta' && key !== 'shift' && key !== 'alt') {
      keys.push(key)
    }
    
    return keys.join('+')
  }
  
  const handleKeyDown = (event) => {
    // Don't trigger shortcuts when typing in inputs
    if (event.target.tagName === 'INPUT' || 
        event.target.tagName === 'TEXTAREA' || 
        event.target.contentEditable === 'true') {
      return
    }
    
    const combo = getKeyCombo(event)
    const handler = shortcuts[combo]
    
    if (handler) {
      event.preventDefault()
      handler(event)
    }
  }
  
  const addShortcut = (combo, handler) => {
    shortcuts[combo] = handler
  }
  
  const removeShortcut = (combo) => {
    delete shortcuts[combo]
  }
  
  const getShortcuts = () => {
    return Object.keys(shortcuts).reduce((acc, combo) => {
      const keys = combo.split('+')
      const description = getShortcutDescription(combo)
      if (description) {
        acc[combo] = {
          keys,
          description
        }
      }
      return acc
    }, {})
  }
  
  const getShortcutDescription = (combo) => {
    const descriptions = {
      'ctrl+z': 'Undo',
      'cmd+z': 'Undo',
      'ctrl+y': 'Redo',
      'cmd+y': 'Redo',
      'ctrl+shift+z': 'Redo',
      'cmd+shift+z': 'Redo',
      'ctrl+c': 'Copy field',
      'cmd+c': 'Copy field',
      'ctrl+v': 'Paste field',
      'cmd+v': 'Paste field',
      'ctrl+d': 'Duplicate field',
      'cmd+d': 'Duplicate field',
      'delete': 'Delete field',
      'backspace': 'Delete field',
      'ctrl+a': 'Select all fields',
      'cmd+a': 'Select all fields',
      'escape': 'Clear selection',
      'ctrl+s': 'Save form',
      'cmd+s': 'Save form',
      'ctrl+p': 'Preview form',
      'cmd+p': 'Preview form'
    }
    
    return descriptions[combo]
  }
  
  onMounted(() => {
    document.addEventListener('keydown', handleKeyDown)
  })
  
  onUnmounted(() => {
    document.removeEventListener('keydown', handleKeyDown)
  })
  
  return {
    addShortcut,
    removeShortcut,
    getShortcuts
  }
}
