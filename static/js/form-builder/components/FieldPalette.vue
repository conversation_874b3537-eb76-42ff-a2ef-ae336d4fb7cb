<template>
  <div class="field-palette">
    <div class="field-palette-header">
      <h3 class="text-lg font-semibold text-gray-900 mb-4">Form Fields</h3>
    </div>
    
    <!-- Basic Fields -->
    <div class="field-palette-section">
      <h4 class="field-palette-title">Basic Fields</h4>
      <div class="space-y-2">
        <div 
          v-for="field in basicFields" 
          :key="field.type"
          class="field-palette-item"
          @click="selectField(field.type)"
          draggable="true"
          @dragstart="onDragStart($event, field.type)"
        >
          <component :is="field.icon" class="field-palette-icon" />
          <div>
            <div class="field-palette-label">{{ field.label }}</div>
            <div class="text-xs text-gray-500">{{ field.description }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Selection Fields -->
    <div class="field-palette-section">
      <h4 class="field-palette-title">Selection Fields</h4>
      <div class="space-y-2">
        <div 
          v-for="field in selectionFields" 
          :key="field.type"
          class="field-palette-item"
          @click="selectField(field.type)"
          draggable="true"
          @dragstart="onDragStart($event, field.type)"
        >
          <component :is="field.icon" class="field-palette-icon" />
          <div>
            <div class="field-palette-label">{{ field.label }}</div>
            <div class="text-xs text-gray-500">{{ field.description }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Advanced Fields -->
    <div class="field-palette-section">
      <h4 class="field-palette-title">Advanced Fields</h4>
      <div class="space-y-2">
        <div 
          v-for="field in advancedFields" 
          :key="field.type"
          class="field-palette-item"
          @click="selectField(field.type)"
          draggable="true"
          @dragstart="onDragStart($event, field.type)"
        >
          <component :is="field.icon" class="field-palette-icon" />
          <div>
            <div class="field-palette-label">{{ field.label }}</div>
            <div class="text-xs text-gray-500">{{ field.description }}</div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- Layout Fields -->
    <div class="field-palette-section">
      <h4 class="field-palette-title">Layout & Content</h4>
      <div class="space-y-2">
        <div 
          v-for="field in layoutFields" 
          :key="field.type"
          class="field-palette-item"
          @click="selectField(field.type)"
          draggable="true"
          @dragstart="onDragStart($event, field.type)"
        >
          <component :is="field.icon" class="field-palette-icon" />
          <div>
            <div class="field-palette-label">{{ field.label }}</div>
            <div class="text-xs text-gray-500">{{ field.description }}</div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import * as Icons from '../icons'

export default {
  name: 'FieldPalette',
  emits: ['field-selected'],
  components: {
    ...Icons
  },
  setup(props, { emit }) {
    const basicFields = ref([
      {
        type: 'text',
        label: 'Text Input',
        description: 'Single line text input',
        icon: 'TextIcon'
      },
      {
        type: 'textarea',
        label: 'Text Area',
        description: 'Multi-line text input',
        icon: 'TextAreaIcon'
      },
      {
        type: 'email',
        label: 'Email',
        description: 'Email address input',
        icon: 'EmailIcon'
      },
      {
        type: 'url',
        label: 'URL',
        description: 'Website URL input',
        icon: 'UrlIcon'
      },
      {
        type: 'number',
        label: 'Number',
        description: 'Numeric input',
        icon: 'NumberIcon'
      },
      {
        type: 'date',
        label: 'Date',
        description: 'Date picker',
        icon: 'DateIcon'
      },
      {
        type: 'time',
        label: 'Time',
        description: 'Time picker',
        icon: 'TimeIcon'
      }
    ])
    
    const selectionFields = ref([
      {
        type: 'select',
        label: 'Dropdown',
        description: 'Dropdown selection',
        icon: 'SelectIcon'
      },
      {
        type: 'radio',
        label: 'Radio Buttons',
        description: 'Single choice selection',
        icon: 'RadioIcon'
      },
      {
        type: 'checkbox',
        label: 'Checkboxes',
        description: 'Multiple choice selection',
        icon: 'CheckboxIcon'
      },
      {
        type: 'checkbox_single',
        label: 'Single Checkbox',
        description: 'Yes/No checkbox',
        icon: 'CheckboxSingleIcon'
      }
    ])
    
    const advancedFields = ref([
      {
        type: 'file',
        label: 'File Upload',
        description: 'File upload field',
        icon: 'FileIcon'
      },
      {
        type: 'image',
        label: 'Image Upload',
        description: 'Image upload field',
        icon: 'ImageIcon'
      },
      {
        type: 'signature',
        label: 'Signature',
        description: 'Digital signature pad',
        icon: 'SignatureIcon'
      },
      {
        type: 'rich_text',
        label: 'Rich Text Editor',
        description: 'WYSIWYG text editor',
        icon: 'RichTextIcon'
      },
      {
        type: 'rating',
        label: 'Rating',
        description: 'Star rating field',
        icon: 'RatingIcon'
      },
      {
        type: 'slider',
        label: 'Slider',
        description: 'Range slider input',
        icon: 'SliderIcon'
      },
      {
        type: 'color',
        label: 'Color Picker',
        description: 'Color selection field',
        icon: 'ColorIcon'
      },
      {
        type: 'phone',
        label: 'Phone Number',
        description: 'Phone number with validation',
        icon: 'PhoneIcon'
      },
      {
        type: 'address',
        label: 'Address',
        description: 'Multi-line address field',
        icon: 'AddressIcon'
      },
      {
        type: 'matrix',
        label: 'Matrix/Grid',
        description: 'Matrix of questions',
        icon: 'MatrixIcon'
      }
    ])
    
    const layoutFields = ref([
      {
        type: 'section',
        label: 'Section Header',
        description: 'Section divider with title',
        icon: 'SectionIcon'
      },
      {
        type: 'html',
        label: 'HTML Content',
        description: 'Custom HTML content',
        icon: 'HtmlIcon'
      },
      {
        type: 'hidden',
        label: 'Hidden Field',
        description: 'Hidden form field',
        icon: 'HiddenIcon'
      }
    ])
    
    const selectField = (fieldType) => {
      emit('field-selected', fieldType)
    }
    
    const onDragStart = (event, fieldType) => {
      event.dataTransfer.setData('text/plain', fieldType)
      event.dataTransfer.effectAllowed = 'copy'
    }
    
    return {
      basicFields,
      selectionFields,
      advancedFields,
      layoutFields,
      selectField,
      onDragStart
    }
  }
}
</script>

<style scoped>
.field-palette {
  @apply p-4;
}

.field-palette-header {
  @apply border-b border-gray-200 pb-4 mb-4;
}

.field-palette-section {
  @apply mb-6;
}

.field-palette-title {
  @apply text-sm font-semibold text-gray-900 mb-3;
}

.field-palette-item {
  @apply flex items-start p-3 bg-gray-50 rounded-md cursor-pointer hover:bg-gray-100 transition-colors;
}

.field-palette-item:hover {
  @apply shadow-sm;
}

.field-palette-item.dragging {
  @apply opacity-50;
}

.field-palette-icon {
  @apply w-5 h-5 mr-3 text-gray-600 flex-shrink-0 mt-0.5;
}

.field-palette-label {
  @apply text-sm font-medium text-gray-700;
}
</style>
